#include <Arduino.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>
#include <EEPROM.h>
#include <RTClib.h>  // RTC library for DS3231/DS1307

// Beautiful Garden Smart Irrigation System - 16x2 LCD Crystal Display
#define LCD_COLS 16
#define LCD_ROWS 2
#define LCD_ADDRESS 0x27
LiquidCrystal_I2C lcd(LCD_ADDRESS, LCD_COLS, LCD_ROWS);

// RTC Module Configuration
RTC_DS3231 rtc;  // DS3231 RTC module (can also use DS1307)

// Hardware Pin Configuration
const int MOISTURE_SENSOR_ZONE1 = A0; // Zone 1 moisture sensor
const int MOISTURE_SENSOR_ZONE2 = A1; // Zone 2 moisture sensor
const int MOISTURE_SENSOR_ZONE3 = A2; // Zone 3 moisture sensor
const int MOISTURE_SENSOR_ZONE4 = A3; // Zone 4 moisture sensor
const int RELAY_ZONE1 = 4;
const int RELAY_ZONE2 = 5;
const int RELAY_ZONE3 = 6;
const int RELAY_ZONE4 = 7;
const int BUTTON1_PIN = 2;  // Navigation button
const int BUTTON2_PIN = 3;  // Action/Settings button
const int BUZZER_PIN = 9;   // Passive Buzzer pin for audio alerts

// System Variables
int moistureZone1, moistureZone2, moistureZone3, moistureZone4;
bool irrigationActive1 = false, irrigationActive2 = false, irrigationActive3 = false, irrigationActive4 = false;
bool manualMode = false;
bool noWaterAlarm1 = false, noWaterAlarm2 = false, noWaterAlarm3 = false, noWaterAlarm4 = false;
bool zoneEnabled1 = true, zoneEnabled2 = true, zoneEnabled3 = true, zoneEnabled4 = true; // Zone enable/disable status
unsigned long irrigationStartTime1 = 0, irrigationStartTime2 = 0, irrigationStartTime3 = 0, irrigationStartTime4 = 0;
unsigned long noWaterAlarmTime1 = 0, noWaterAlarmTime2 = 0, noWaterAlarmTime3 = 0, noWaterAlarmTime4 = 0; // Water alarm timing
unsigned long lastSensorRead = 0, lastDisplayUpdate = 0;
unsigned long button1PressTime = 0, button2PressTime = 0;
bool button1Pressed = false, button2Pressed = false;
bool button1LongPressDetected = false, button2LongPressDetected = false;
int displayMode = 0; // 0: Main, 1: Status, 2-5: Zone Settings, 6-9: Zone Control, 10: Buzzer Settings, 11: Smart Schedule Settings
int animationFrame = 0;
bool blinkState = false;

// Smart Seasonal Irrigation Schedule System Variables
bool smartScheduleEnabled = false; // Smart schedule enable/disable status
DateTime currentTime; // Current time from RTC
int currentSeason = 0; // 0: Winter, 1: Spring, 2: Summer, 3: Autumn
bool isIrrigationTimeAllowed = true; // Whether current time allows irrigation

// Seasonal Irrigation Time Windows
// Winter ❄️ (Dec-Feb): 09:00-12:00
// Spring 🌱 (Mar-May): 06:00-10:00
// Summer ☀️ (Jun-Aug): 05:00-08:00 & 18:00-20:00
// Autumn 🍂 (Sep-Nov): 07:00-10:00

// Buzzer System Variables
bool buzzerEnabled = true; // Buzzer enable/disable status
unsigned long lastBuzzerTime = 0;
int buzzerPattern = 0; // 0: Off, 1: Water alarm, 2: Irrigation start, 3: System ready, 4: Button press, 5: Settings change, 6: Welcome melody, 7: Success sound, 8: Zone activation, 9: Happy irrigation, 11: Intro sound, 12: Team sound
int buzzerPatternStep = 0;
unsigned long buzzerPatternStartTime = 0;

// Water alarm system - single alert per detection

// Display Optimization Variables
int lastDisplayMode = -1; // Track display mode changes
bool forceDisplayUpdate = false; // Force update flag
unsigned long lastBlinkUpdate = 0;
bool lastBlinkState = false;

// System Settings - Moisture threshold percentages for each zone (10-100%)
int moistureThresholdPercentZone1 = 30;  // Zone 1 moisture threshold (30%)
int moistureThresholdPercentZone2 = 30;  // Zone 2 moisture threshold (30%)
int moistureThresholdPercentZone3 = 30;  // Zone 3 moisture threshold (30%)
int moistureThresholdPercentZone4 = 30;  // Zone 4 moisture threshold (30%)
const unsigned long WATERING_DURATION = 60000UL;
const unsigned long SENSOR_READ_INTERVAL = 2000UL;
const unsigned long DISPLAY_UPDATE_INTERVAL = 100UL;  // Faster refresh rate: 100ms instead of 200ms
const unsigned long NO_WATER_RETRY_INTERVAL = 600000UL; // 10 minutes retry interval

// EEPROM Memory Addresses for Settings Storage
#define EEPROM_MAGIC_NUMBER 0xAB12        // Magic number to verify valid data
#define EEPROM_ADDR_MAGIC 0               // Address for magic number (2 bytes)
#define EEPROM_ADDR_ZONE1_THRESHOLD 2     // Address for Zone 1 threshold (1 byte)
#define EEPROM_ADDR_ZONE2_THRESHOLD 3     // Address for Zone 2 threshold (1 byte)
#define EEPROM_ADDR_ZONE3_THRESHOLD 4     // Address for Zone 3 threshold (1 byte)
#define EEPROM_ADDR_ZONE4_THRESHOLD 5     // Address for Zone 4 threshold (1 byte)
#define EEPROM_ADDR_ZONE1_ENABLED 6       // Address for Zone 1 enabled status (1 byte)
#define EEPROM_ADDR_ZONE2_ENABLED 7       // Address for Zone 2 enabled status (1 byte)
#define EEPROM_ADDR_ZONE3_ENABLED 8       // Address for Zone 3 enabled status (1 byte)
#define EEPROM_ADDR_ZONE4_ENABLED 9       // Address for Zone 4 enabled status (1 byte)
#define EEPROM_ADDR_BUZZER_ENABLED 10     // Address for buzzer enabled status (1 byte)
#define EEPROM_ADDR_SMART_SCHEDULE 11     // Address for smart schedule enabled status (1 byte)

// Function Declarations
void readSensors();
void checkButtons();
void handleButton1Press();
void handleButton2Press();
void handleButton2LongPress();
void autoIrrigation();
void checkNoWaterAlarmReset(); // Water alarm reset check function
void startIrrigation(int zone);
void checkIrrigationStop();
void stopIrrigation(int zone, String reason);
void updateDisplay();
void showAnimatedIntro(); // Beautiful animated intro
void showWelcome();
void showMainScreen();
void showStatusScreen();
void showSettingsScreen();
void showZone1ControlScreen();
void showZone2ControlScreen();
void showZone3ControlScreen();
void showZone4ControlScreen();
void showBuzzerSettingsScreen();
void showSmartScheduleScreen();

// Smart Seasonal Schedule Functions
void initializeRTC();
void updateCurrentTime();
int getCurrentSeason(int month);
bool isIrrigationAllowed();
String getSeasonName(int season);
String getSeasonIcon(int season);
String getCurrentTimeString();
String getCurrentDateString();

// Buzzer System Functions
void playBuzzerPattern(int pattern);
void updateBuzzer();
void playTone(int frequency, int duration);
void playWaterAlarmSound();
void playIrrigationStartSound();
void playSystemReadySound();
void playButtonPressSound();
void playSettingsChangeSound();
void playWelcomeMelody();
void playSuccessSound();
void playZoneActivationSound();
void playHappyIrrigationSound();
void playIntroSound();
void playTeamSound();

// LCD Display Functions
void drawMoistureBar(int col, int row, int moisturePercent);
void drawProgressBar(int col, int row, int progress, int maxVal);
void showLoadingAnimation();

// EEPROM Settings Functions
void saveSettingsToEEPROM();
void loadSettingsFromEEPROM();
void resetSettingsToDefault();

// Beautiful Custom Characters for Garden Theme
byte plantChar[8] = {
  0b00100,
  0b01110,
  0b10101,
  0b00100,
  0b00100,
  0b00100,
  0b01110,
  0b11111
};

byte waterDropChar[8] = {
  0b00100,
  0b00100,
  0b01010,
  0b01010,
  0b10001,
  0b10001,
  0b01110,
  0b00000
};

byte heartChar[8] = {
  0b00000,
  0b01010,
  0b11111,
  0b11111,
  0b01110,
  0b00100,
  0b00000,
  0b00000
};

byte alertChar[8] = {
  0b00100,
  0b01110,
  0b01110,
  0b01110,
  0b00100,
  0b00000,
  0b00100,
  0b00000
};

byte starChar[8] = {
  0b00100,
  0b10101,
  0b01110,
  0b11111,
  0b01110,
  0b10101,
  0b00100,
  0b00000
};

byte leafChar[8] = {
  0b00000,
  0b00100,
  0b01110,
  0b11111,
  0b01110,
  0b00100,
  0b00000,
  0b00000
};

byte sunChar[8] = {
  0b10101,
  0b01110,
  0b11111,
  0b11111,
  0b11111,
  0b01110,
  0b10101,
  0b00000
};

byte emptyBoxChar[8] = {
  0b11111,
  0b10001,
  0b10001,
  0b10001,
  0b10001,
  0b10001,
  0b10001,
  0b11111
};

void setup() {
  // Safety First: Turn off all relays immediately (Active Low Trigger)
  pinMode(RELAY_ZONE1, OUTPUT);
  pinMode(RELAY_ZONE2, OUTPUT);
  pinMode(RELAY_ZONE3, OUTPUT);
  pinMode(RELAY_ZONE4, OUTPUT);
  digitalWrite(RELAY_ZONE1, HIGH);  // Turn off relay 1 (Active Low Trigger)
  digitalWrite(RELAY_ZONE2, HIGH);  // Turn off relay 2 (Active Low Trigger)
  digitalWrite(RELAY_ZONE3, HIGH);  // Turn off relay 3 (Active Low Trigger)
  digitalWrite(RELAY_ZONE4, HIGH);  // Turn off relay 4 (Active Low Trigger)

  // Initialize Beautiful LCD Crystal Display
  lcd.init();
  lcd.backlight();
  lcd.clear();

  // Initialize RTC Module
  initializeRTC();

  // Create Beautiful Garden-Themed Custom Characters
  lcd.createChar(0, plantChar);      // Plant symbol
  lcd.createChar(1, waterDropChar);  // Water drop symbol
  lcd.createChar(2, heartChar);      // Heart symbol
  lcd.createChar(3, alertChar);      // Alert symbol
  lcd.createChar(4, starChar);       // Star symbol
  lcd.createChar(5, leafChar);       // Leaf symbol
  lcd.createChar(6, sunChar);        // Sun symbol
  lcd.createChar(7, emptyBoxChar);   // Empty box symbol

  // Initialize remaining pins
  pinMode(BUTTON1_PIN, INPUT_PULLUP);  // Navigation button
  pinMode(BUTTON2_PIN, INPUT_PULLUP);  // Action/Settings button
  pinMode(BUZZER_PIN, OUTPUT);
  digitalWrite(BUZZER_PIN, LOW); // Ensure buzzer is off initially

  // Load Settings from EEPROM with visual feedback
  lcd.setCursor(0, 0);
  lcd.print("Loading Settings");
  lcd.setCursor(0, 1);
  lcd.print("Please Wait...");
  delay(1000);

  loadSettingsFromEEPROM();

  lcd.setCursor(0, 1);
  lcd.print("Settings Loaded!");
  delay(1000);
  lcd.clear();

  // Show Beautiful Animated Intro with sounds
  showAnimatedIntro();

  // Show Welcome Screen with beautiful melody
  showWelcome();

  // Play welcome melody during welcome screen
  playBuzzerPattern(6); // Welcome melody
  delay(2000);

  // Play system ready sound
  playBuzzerPattern(3); // System ready sound
}

void loop() {
  unsigned long currentTime = millis();

  // Update RTC time and seasonal schedule
  updateCurrentTime();

  if(currentTime - lastSensorRead >= SENSOR_READ_INTERVAL) {
    readSensors();
    lastSensorRead = currentTime;
  }
  
  checkButtons();
  checkNoWaterAlarmReset(); // فحص إعادة تعيين تحذير نقص الماء

  if(!manualMode) {
    autoIrrigation();
  }
  
  // Smart display update - faster refresh with optimized clearing
  if(currentTime - lastDisplayUpdate >= DISPLAY_UPDATE_INTERVAL) {
    // Check if display mode changed
    if(displayMode != lastDisplayMode) {
      forceDisplayUpdate = true;
      lastDisplayMode = displayMode;
    }

    // Update animation frame and blink state
    animationFrame++;
    blinkState = (animationFrame / 5) % 2; // Faster blinking: every 5 frames instead of 10

    // Check if blink state changed
    if(blinkState != lastBlinkState) {
      forceDisplayUpdate = true;
      lastBlinkState = blinkState;
    }

    // Update display
    updateDisplay();
    lastDisplayUpdate = currentTime;
    forceDisplayUpdate = false;
  }

  // Update buzzer patterns
  updateBuzzer();

  delay(50); // Reduced delay for better responsiveness: 50ms instead of 100ms
}

void readSensors() {
  // قراءة المناطق الأربع (حساس واحد لكل منطقة)
  moistureZone1 = analogRead(MOISTURE_SENSOR_ZONE1);
  moistureZone2 = analogRead(MOISTURE_SENSOR_ZONE2);
  moistureZone3 = analogRead(MOISTURE_SENSOR_ZONE3);
  moistureZone4 = analogRead(MOISTURE_SENSOR_ZONE4);
}

void checkButtons() {
  static unsigned long button1PressStartTime = 0, button2PressStartTime = 0;
  static bool button1WasPressed = false, button2WasPressed = false;

  // فحص الزر الأول (التنقل)
  bool button1State = (digitalRead(BUTTON1_PIN) == LOW);
  if(button1State && !button1WasPressed) {
    button1PressStartTime = millis();
    button1WasPressed = true;
  }
  if(!button1State && button1WasPressed) {
    unsigned long pressDuration = millis() - button1PressStartTime;
    button1WasPressed = false;
    if(pressDuration < 800) {
      handleButton1Press();
    }
    delay(200); // منع الارتداد
  }

  // فحص الزر الثاني (الإعدادات/التحكم)
  bool button2State = (digitalRead(BUTTON2_PIN) == LOW);
  if(button2State && !button2WasPressed) {
    button2PressStartTime = millis();
    button2WasPressed = true;
    button2LongPressDetected = false;
  }

  // فحص الضغط الطويل للزر الثاني
  if(button2State && button2WasPressed) {
    unsigned long pressDuration = millis() - button2PressStartTime;
    if(pressDuration > 800 && !button2LongPressDetected) {
      button2LongPressDetected = true;
      handleButton2LongPress();
    }
  }

  if(!button2State && button2WasPressed) {
    unsigned long pressDuration = millis() - button2PressStartTime;
    button2WasPressed = false;
    if(pressDuration < 800 && !button2LongPressDetected) {
      handleButton2Press();
    }
    delay(200); // منع الارتداد
  }
}

// Handle Button 1 Press - Navigation
void handleButton1Press() {
  // Play button press sound
  playBuzzerPattern(4); // Button press sound

  // التنقل بين الشاشات
  displayMode++;
  if(displayMode > 11) {
    displayMode = 0; // العودة للشاشة الرئيسية
  }

  // Force immediate display update for responsive navigation
  forceDisplayUpdate = true;
}

// Handle Button 2 Press - Quick Actions
void handleButton2Press() {
  // Play button press sound
  playBuzzerPattern(4); // Button press sound

  if(displayMode <= 1) {
    // On normal screens - toggle between auto and manual mode
    manualMode = !manualMode;

    // Play different sounds for different modes
    if(manualMode) {
      playBuzzerPattern(8); // Zone activation sound for manual mode
    } else {
      playBuzzerPattern(6); // Welcome melody for auto mode
    }

    // Stop all relays when switching to manual mode (Active Low Trigger)
    if(manualMode) {
      digitalWrite(RELAY_ZONE1, HIGH); // Turn off relay 1 (Active Low Trigger)
      digitalWrite(RELAY_ZONE2, HIGH); // Turn off relay 2 (Active Low Trigger)
      digitalWrite(RELAY_ZONE3, HIGH); // Turn off relay 3 (Active Low Trigger)
      digitalWrite(RELAY_ZONE4, HIGH); // Turn off relay 4 (Active Low Trigger)
      irrigationActive1 = false;
      irrigationActive2 = false;
      irrigationActive3 = false;
      irrigationActive4 = false;
    }
  }

  // Force immediate display update for responsive settings change
  forceDisplayUpdate = true;
}

// Handle Button 2 Long Press - Settings Changes
void handleButton2LongPress() {
  // Play settings change sound
  playBuzzerPattern(5); // Settings change sound

  if(displayMode == 2) {
    // Zone 1 settings - change moisture threshold percentage (10-100%)
    moistureThresholdPercentZone1 += 10;
    if(moistureThresholdPercentZone1 > 100) {
      moistureThresholdPercentZone1 = 10;
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 3) {
    // Zone 2 settings - change moisture threshold percentage (10-100%)
    moistureThresholdPercentZone2 += 10;
    if(moistureThresholdPercentZone2 > 100) {
      moistureThresholdPercentZone2 = 10;
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 4) {
    // Zone 3 settings - change moisture threshold percentage (10-100%)
    moistureThresholdPercentZone3 += 10;
    if(moistureThresholdPercentZone3 > 100) {
      moistureThresholdPercentZone3 = 10;
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 5) {
    // Zone 4 settings - change moisture threshold percentage (10-100%)
    moistureThresholdPercentZone4 += 10;
    if(moistureThresholdPercentZone4 > 100) {
      moistureThresholdPercentZone4 = 10;
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 6) {
    // Zone 1 control screen - toggle Zone 1 enable/disable
    zoneEnabled1 = !zoneEnabled1;
    // Play zone activation sound
    if(zoneEnabled1) {
      playBuzzerPattern(8); // Zone activation sound
    }
    // Stop irrigation if zone is disabled
    if(!zoneEnabled1 && irrigationActive1) {
      stopIrrigation(1, "Zone Disabled");
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 7) {
    // Zone 2 control screen - toggle Zone 2 enable/disable
    zoneEnabled2 = !zoneEnabled2;
    // Play zone activation sound
    if(zoneEnabled2) {
      playBuzzerPattern(8); // Zone activation sound
    }
    // Stop irrigation if zone is disabled
    if(!zoneEnabled2 && irrigationActive2) {
      stopIrrigation(2, "Zone Disabled");
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 8) {
    // Zone 3 control screen - toggle Zone 3 enable/disable
    zoneEnabled3 = !zoneEnabled3;
    // Play zone activation sound
    if(zoneEnabled3) {
      playBuzzerPattern(8); // Zone activation sound
    }
    // Stop irrigation if zone is disabled
    if(!zoneEnabled3 && irrigationActive3) {
      stopIrrigation(3, "Zone Disabled");
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 9) {
    // Zone 4 control screen - toggle Zone 4 enable/disable
    zoneEnabled4 = !zoneEnabled4;
    // Play zone activation sound
    if(zoneEnabled4) {
      playBuzzerPattern(8); // Zone activation sound
    }
    // Stop irrigation if zone is disabled
    if(!zoneEnabled4 && irrigationActive4) {
      stopIrrigation(4, "Zone Disabled");
    }
    saveSettingsToEEPROM(); // Save to EEPROM immediately
  } else if(displayMode == 10) {
    // Buzzer settings screen - toggle buzzer enable/disable
    buzzerEnabled = !buzzerEnabled;
    saveSettingsToEEPROM(); // Save to EEPROM immediately
    // Play test sound if buzzer was enabled
    if(buzzerEnabled) {
      playBuzzerPattern(4); // Button press sound as test
    }
  } else if(displayMode == 11) {
    // Smart schedule settings screen - toggle smart schedule enable/disable
    smartScheduleEnabled = !smartScheduleEnabled;
    saveSettingsToEEPROM(); // Save to EEPROM immediately
    // Play confirmation sound
    if(smartScheduleEnabled) {
      playBuzzerPattern(8); // Zone activation sound for smart schedule enabled
    } else {
      playBuzzerPattern(4); // Button press sound for disabled
    }
  }

  // Force immediate display update for responsive settings change
  forceDisplayUpdate = true;
}

void checkNoWaterAlarmReset() {
  unsigned long currentTime = millis();

  // فحص إعادة تعيين تحذير نقص الماء للمنطقة الأولى
  if(noWaterAlarm1 && (currentTime - noWaterAlarmTime1 >= NO_WATER_RETRY_INTERVAL)) {
    noWaterAlarm1 = false;
  }

  // فحص إعادة تعيين تحذير نقص الماء للمنطقة الثانية
  if(noWaterAlarm2 && (currentTime - noWaterAlarmTime2 >= NO_WATER_RETRY_INTERVAL)) {
    noWaterAlarm2 = false;
  }

  // فحص إعادة تعيين تحذير نقص الماء للمنطقة الثالثة
  if(noWaterAlarm3 && (currentTime - noWaterAlarmTime3 >= NO_WATER_RETRY_INTERVAL)) {
    noWaterAlarm3 = false;
  }

  // فحص إعادة تعيين تحذير نقص الماء للمنطقة الرابعة
  if(noWaterAlarm4 && (currentTime - noWaterAlarmTime4 >= NO_WATER_RETRY_INTERVAL)) {
    noWaterAlarm4 = false;
  }
}



void autoIrrigation() {
  // تحويل النسب المئوية إلى قيم الحساس
  int currentMoisturePercent1 = map(1023 - moistureZone1, 0, 1023, 0, 100);
  int currentMoisturePercent2 = map(1023 - moistureZone2, 0, 1023, 0, 100);
  int currentMoisturePercent3 = map(1023 - moistureZone3, 0, 1023, 0, 100);
  int currentMoisturePercent4 = map(1023 - moistureZone4, 0, 1023, 0, 100);

  // Check if irrigation is allowed based on smart schedule (if enabled)
  bool timeAllowed = true;
  if(smartScheduleEnabled) {
    timeAllowed = isIrrigationAllowed();
  }

  // المنطقة الأولى - بدء الري إذا انخفضت الرطوبة عن النسبة المرجعية (فقط إذا كانت مفعلة والوقت مناسب)
  if(zoneEnabled1 && currentMoisturePercent1 < moistureThresholdPercentZone1 && !irrigationActive1 && !noWaterAlarm1 && timeAllowed) {
    startIrrigation(1);
  }

  // المنطقة الثانية - بدء الري إذا انخفضت الرطوبة عن النسبة المرجعية (فقط إذا كانت مفعلة والوقت مناسب)
  if(zoneEnabled2 && currentMoisturePercent2 < moistureThresholdPercentZone2 && !irrigationActive2 && !noWaterAlarm2 && timeAllowed) {
    startIrrigation(2);
  }

  // المنطقة الثالثة - بدء الري إذا انخفضت الرطوبة عن النسبة المرجعية (فقط إذا كانت مفعلة والوقت مناسب)
  if(zoneEnabled3 && currentMoisturePercent3 < moistureThresholdPercentZone3 && !irrigationActive3 && !noWaterAlarm3 && timeAllowed) {
    startIrrigation(3);
  }

  // المنطقة الرابعة - بدء الري إذا انخفضت الرطوبة عن النسبة المرجعية (فقط إذا كانت مفعلة والوقت مناسب)
  if(zoneEnabled4 && currentMoisturePercent4 < moistureThresholdPercentZone4 && !irrigationActive4 && !noWaterAlarm4 && timeAllowed) {
    startIrrigation(4);
  }

  checkIrrigationStop();
}

void startIrrigation(int zone) {
  // Play happy irrigation sound for more fun
  playBuzzerPattern(9); // Happy irrigation sound

  if(zone == 1) {
    digitalWrite(RELAY_ZONE1, LOW);   // Turn on relay (Active Low Trigger)
    irrigationActive1 = true;
    irrigationStartTime1 = millis();
    noWaterAlarm1 = false;
    noWaterAlarmTime1 = 0; // Reset alarm timer
  } else if(zone == 2) {
    digitalWrite(RELAY_ZONE2, LOW);   // Turn on relay (Active Low Trigger)
    irrigationActive2 = true;
    irrigationStartTime2 = millis();
    noWaterAlarm2 = false;
    noWaterAlarmTime2 = 0; // Reset alarm timer
  } else if(zone == 3) {
    digitalWrite(RELAY_ZONE3, LOW);   // Turn on relay (Active Low Trigger)
    irrigationActive3 = true;
    irrigationStartTime3 = millis();
    noWaterAlarm3 = false;
    noWaterAlarmTime3 = 0; // Reset alarm timer
  } else if(zone == 4) {
    digitalWrite(RELAY_ZONE4, LOW);   // Turn on relay (Active Low Trigger)
    irrigationActive4 = true;
    irrigationStartTime4 = millis();
    noWaterAlarm4 = false;
    noWaterAlarmTime4 = 0; // Reset alarm timer
  }

  // Force display update for immediate status change
  forceDisplayUpdate = true;
}

void checkIrrigationStop() {
  unsigned long currentTime = millis();

  // تحويل قراءات الحساسات إلى نسب مئوية
  int currentMoisturePercent1 = map(1023 - moistureZone1, 0, 1023, 0, 100);
  int currentMoisturePercent2 = map(1023 - moistureZone2, 0, 1023, 0, 100);
  int currentMoisturePercent3 = map(1023 - moistureZone3, 0, 1023, 0, 100);
  int currentMoisturePercent4 = map(1023 - moistureZone4, 0, 1023, 0, 100);

  // Check Zone 1 - stop irrigation when target moisture + 10% is reached
  if(irrigationActive1) {
    if(currentMoisturePercent1 >= (moistureThresholdPercentZone1 + 10)) {
      playBuzzerPattern(7); // Success sound when target reached
      stopIrrigation(1, "Target Moisture Reached");
    } else if(currentTime - irrigationStartTime1 >= WATERING_DURATION) {
      stopIrrigation(1, "Duration Complete - Check Water");
      noWaterAlarm1 = true;
      noWaterAlarmTime1 = currentTime; // Record alarm activation time
      playBuzzerPattern(1); // Water alarm sound - single alert per detection
    }
  }

  // Check Zone 2 - stop irrigation when target moisture + 10% is reached
  if(irrigationActive2) {
    if(currentMoisturePercent2 >= (moistureThresholdPercentZone2 + 10)) {
      playBuzzerPattern(7); // Success sound when target reached
      stopIrrigation(2, "Target Moisture Reached");
    } else if(currentTime - irrigationStartTime2 >= WATERING_DURATION) {
      stopIrrigation(2, "Duration Complete - Check Water");
      noWaterAlarm2 = true;
      noWaterAlarmTime2 = currentTime; // Record alarm activation time
      playBuzzerPattern(1); // Water alarm sound - single alert per detection
    }
  }

  // Check Zone 3 - stop irrigation when target moisture + 10% is reached
  if(irrigationActive3) {
    if(currentMoisturePercent3 >= (moistureThresholdPercentZone3 + 10)) {
      playBuzzerPattern(7); // Success sound when target reached
      stopIrrigation(3, "Target Moisture Reached");
    } else if(currentTime - irrigationStartTime3 >= WATERING_DURATION) {
      stopIrrigation(3, "Duration Complete - Check Water");
      noWaterAlarm3 = true;
      noWaterAlarmTime3 = currentTime; // Record alarm activation time
      playBuzzerPattern(1); // Water alarm sound - single alert per detection
    }
  }

  // Check Zone 4 - stop irrigation when target moisture + 10% is reached
  if(irrigationActive4) {
    if(currentMoisturePercent4 >= (moistureThresholdPercentZone4 + 10)) {
      playBuzzerPattern(7); // Success sound when target reached
      stopIrrigation(4, "Target Moisture Reached");
    } else if(currentTime - irrigationStartTime4 >= WATERING_DURATION) {
      stopIrrigation(4, "Duration Complete - Check Water");
      noWaterAlarm4 = true;
      noWaterAlarmTime4 = currentTime; // Record alarm activation time
      playBuzzerPattern(1); // Water alarm sound - single alert per detection
    }
  }
}

void stopIrrigation(int zone, String reason) {
  if(zone == 1) {
    digitalWrite(RELAY_ZONE1, HIGH);  // Turn off relay (Active Low Trigger)
    irrigationActive1 = false;
  } else if(zone == 2) {
    digitalWrite(RELAY_ZONE2, HIGH);  // Turn off relay (Active Low Trigger)
    irrigationActive2 = false;
  } else if(zone == 3) {
    digitalWrite(RELAY_ZONE3, HIGH);  // Turn off relay (Active Low Trigger)
    irrigationActive3 = false;
  } else if(zone == 4) {
    digitalWrite(RELAY_ZONE4, HIGH);  // Turn off relay (Active Low Trigger)
    irrigationActive4 = false;
  }

  // Force display update for immediate status change
  forceDisplayUpdate = true;
}

void updateDisplay() {
  // Smart clearing - only clear when display mode changes or forced update
  if(forceDisplayUpdate || displayMode != lastDisplayMode) {
    lcd.clear();
  }

  switch(displayMode) {
    case 0: showMainScreen(); break;
    case 1: showStatusScreen(); break;
    case 2:
    case 3:
    case 4:
    case 5: showSettingsScreen(); break;
    case 6: showZone1ControlScreen(); break;
    case 7: showZone2ControlScreen(); break;
    case 8: showZone3ControlScreen(); break;
    case 9: showZone4ControlScreen(); break;
    case 10: showBuzzerSettingsScreen(); break;
    case 11: showSmartScheduleScreen(); break;
  }
}

void showAnimatedIntro() {
  // Clean & Fast Animated Intro - Empty Boxes Only with Beautiful Sounds

  // Phase 1: Simple Loading with Empty Boxes Only
  lcd.clear();
  playBuzzerPattern(11); // Intro sound
  delay(800); // Wait for intro sound to play

  // Clean empty box loading pattern with subtle system sounds
  for(int wave = 0; wave < 2; wave++) {
    for(int i = 0; i < 16; i++) {
      lcd.setCursor(i, 0);
      lcd.write(byte(7)); // Empty box
      lcd.setCursor(i, 1);
      lcd.write(byte(7)); // Empty box

      // LOUD professional loading sound - MAXIMUM VOLUME
      if(i % 4 == 0) playTone(1500, 50); // DOUBLED frequency & duration - MUCH LOUDER
      delay(15);
    }
    delay(100);

    // Quick clear
    for(int i = 0; i < 16; i++) {
      lcd.setCursor(i, 0);
      lcd.print(" ");
      lcd.setCursor(i, 1);
      lcd.print(" ");
      delay(10);
    }
    delay(50);
  }

  // Simple matrix with empty boxes only and professional initialization sounds
  lcd.clear();
  for(int matrix = 0; matrix < 8; matrix++) {
    lcd.setCursor(random(0, 16), random(0, 2));
    lcd.write(byte(7)); // Empty box only

    // LOUD system initialization sound - MAXIMUM VOLUME
    if(matrix % 2 == 0) playTone(1700, 60); // DOUBLED frequency & duration - MUCH LOUDER
    delay(50);
    if(matrix > 4) {
      lcd.setCursor(random(0, 16), random(0, 2));
      lcd.print(" "); // Clear random boxes
    }
  }
  delay(300);

  // Phase 2: Clean HS TEAM Presentation
  lcd.clear();
  delay(150);

  // Simple frame with empty boxes only
  for(int frame = 0; frame < 16; frame++) {
    lcd.setCursor(frame, 0);
    lcd.write(byte(7)); // Empty box border
    lcd.setCursor(frame, 1);
    lcd.write(byte(7)); // Empty box border
    delay(15);
  }
  delay(100);

  // Clear center for text
  for(int clear = 2; clear < 14; clear++) {
    lcd.setCursor(clear, 0);
    lcd.print(" ");
    delay(8);
  }

  // Simple HS TEAM typing
  String hsTeam = "HS TEAM";
  int hsStartPos = (16 - hsTeam.length()) / 2;

  // Play team sound
  playBuzzerPattern(12); // Team sound
  delay(200); // Give time for sound to start

  // Simple cursor effect with empty box
  for(int cursor = 0; cursor < 2; cursor++) {
    lcd.setCursor(hsStartPos, 0);
    lcd.write(byte(7)); // Empty box cursor
    delay(120);
    lcd.setCursor(hsStartPos, 0);
    lcd.print(" ");
    delay(120);
  }

  // Clean character reveal with subtle professional sounds
  for(int i = 0; i < (int)hsTeam.length(); i++) {
    lcd.setCursor(hsStartPos + i, 0);
    lcd.print(hsTeam.charAt(i));

    // LOUD professional typing sound - MAXIMUM VOLUME
    if(hsTeam.charAt(i) != ' ') { // Only for actual characters
      playTone(1900, 80); // DOUBLED frequency & duration - MUCH LOUDER
    }

    // Simple highlight with empty box
    delay(50);
    lcd.setCursor(hsStartPos + i, 0);
    lcd.write(byte(7)); // Empty box highlight
    delay(30);
    lcd.setCursor(hsStartPos + i, 0);
    lcd.print(hsTeam.charAt(i)); // Restore character
    delay(100);
  }

  delay(600);

  // Simple pulsing with empty boxes only
  for(int pulse = 0; pulse < 3; pulse++) {
    // Pulse corners with empty boxes only
    lcd.setCursor(0, 0);
    lcd.write(byte(7)); // Empty box
    lcd.setCursor(15, 0);
    lcd.write(byte(7)); // Empty box
    lcd.setCursor(0, 1);
    lcd.write(byte(7)); // Empty box
    lcd.setCursor(15, 1);
    lcd.write(byte(7)); // Empty box
    delay(100);

    // Clear corners
    lcd.setCursor(0, 0);
    lcd.print(" ");
    lcd.setCursor(15, 0);
    lcd.print(" ");
    lcd.setCursor(0, 1);
    lcd.print(" ");
    lcd.setCursor(15, 1);
    lcd.print(" ");
    delay(100);
  }

  delay(300);

  // Phase 3: Siraj Al-Ma'rifa then MADE IN IRAQ
  // Simple screen wipe with empty boxes and LOUD professional transition
  playTone(1800, 120); // LOUD professional transition sound - DOUBLED frequency & duration
  for(int wipe = 0; wipe < 16; wipe++) {
    lcd.setCursor(wipe, 0);
    lcd.write(byte(7)); // Empty box
    lcd.setCursor(15-wipe, 1);
    lcd.write(byte(7)); // Empty box
    // LOUD sound feedback - MAXIMUM VOLUME
    if(wipe == 8) playTone(1900, 80); // LOUD mid-transition confirmation - DOUBLED
    delay(20);
  }
  delay(150);

  // Quick reveal transition
  for(int reveal = 0; reveal < 2; reveal++) {
    lcd.noBacklight();
    delay(60);
    lcd.backlight();
    delay(60);
  }

  lcd.clear();

  // First: Type Siraj Al-Ma'rifa
  String sirajText = "Siraj Al-Ma'rifa";
  int sirajStartPos = 0; // Start from left as text is long

  // Simple corners with empty boxes
  lcd.setCursor(0, 1);
  lcd.write(byte(7)); // Empty box corner
  lcd.setCursor(15, 1);
  lcd.write(byte(7)); // Empty box corner

  // Type Siraj Al-Ma'rifa character by character with professional feedback
  for(unsigned int i = 0; i < sirajText.length(); i++) {
    lcd.setCursor(sirajStartPos + i, 0);
    lcd.print(sirajText.charAt(i));

    // LOUD professional typing feedback - MAXIMUM VOLUME
    if(sirajText.charAt(i) != ' ' && i % 3 == 0) { // Less frequent, more professional
      playTone(1800, 70); // LOUD professional tone - DOUBLED frequency & duration
    }
    delay(80);
  }

  delay(300); // Short pause after completing Siraj Al-Ma'rifa

  // Then: Type MADE IN IRAQ underneath
  String madeText = "MADE IN IRAQ";
  int madeStartPos = 2;  // Center the shorter text

  // Clear bottom corners and type MADE IN IRAQ
  lcd.setCursor(0, 1);
  lcd.print(" ");
  lcd.setCursor(15, 1);
  lcd.print(" ");

  for(unsigned int i = 0; i < madeText.length(); i++) {
    lcd.setCursor(madeStartPos + i, 1);
    lcd.print(madeText.charAt(i));

    // LOUD typing feedback for MADE IN IRAQ - MAXIMUM VOLUME
    if(madeText.charAt(i) != ' ' && i % 2 == 0) { // Professional frequency
      playTone(1900, 70); // LOUD professional tone - DOUBLED frequency & duration
    }
    delay(70);
  }

  delay(800);

  // Final Phase: LOUD system completion - MAXIMUM VOLUME
  playTone(2000, 200); // LOUD completion tone - DOUBLED frequency & duration
  delay(150);

  // Simple fade out with empty boxes - minimal professional feedback
  for(int fade = 0; fade < 16; fade++) {
    lcd.setCursor(fade, 0);
    lcd.write(byte(7)); // Empty box
    lcd.setCursor(15-fade, 1);
    lcd.write(byte(7)); // Empty box

    // LOUD professional completion feedback - MAXIMUM VOLUME
    if(fade == 15) playTone(1900, 100); // LOUD final confirmation - DOUBLED frequency & duration
    delay(30);
  }

  delay(200);

  // Clean clear
  for(int clear = 0; clear < 16; clear++) {
    lcd.setCursor(clear, 0);
    lcd.print(" ");
    lcd.setCursor(clear, 1);
    lcd.print(" ");
    delay(20);
  }

  delay(300);
}

void showWelcome() {
  lcd.clear();

  // Beautiful Agricultural Welcome Screen for 16x2 LCD
  lcd.setCursor(0, 0);
  lcd.write(byte(6)); // Sun symbol
  lcd.print(" Smart Farm ");
  lcd.write(byte(6)); // Sun symbol

  lcd.setCursor(0, 1);
  lcd.write(byte(1)); // Water drop symbol
  lcd.print(" Irrigation ");
  lcd.write(byte(0)); // Plant symbol

  // Brief welcome animation with agricultural theme
  delay(500);
  for(int i = 0; i < 3; i++) {
    lcd.setCursor(0, 0);
    lcd.write(byte(4)); // Star
    lcd.setCursor(15, 0);
    lcd.write(byte(4)); // Star
    lcd.setCursor(0, 1);
    lcd.write(byte(5)); // Leaf
    lcd.setCursor(15, 1);
    lcd.write(byte(7)); // Flower
    delay(300);
    lcd.setCursor(0, 0);
    lcd.write(byte(6)); // Sun
    lcd.setCursor(15, 0);
    lcd.write(byte(6)); // Sun
    lcd.setCursor(0, 1);
    lcd.write(byte(1)); // Water drop
    lcd.setCursor(15, 1);
    lcd.write(byte(0)); // Plant
    delay(300);
  }
}

// Beautiful Moisture Bar Function for 16x2 LCD
void drawMoistureBar(int col, int row, int moisturePercent) {
  lcd.setCursor(col, row);

  // Draw moisture bar with symbols (compact for 16x2 screen)
  int barLength = 4; // Compact bar length
  int filledBars = map(moisturePercent, 0, 100, 0, barLength);

  lcd.print("[");
  for(int i = 0; i < barLength; i++) {
    if(i < filledBars) {
      lcd.print("="); // Filled portion
    } else {
      lcd.print("-"); // Empty portion
    }
  }
  lcd.print("]");
}

// Progress Bar Function for Loading Animations
void drawProgressBar(int col, int row, int progress, int maxVal) {
  lcd.setCursor(col, row);
  int barLength = 6;
  int filledBars = map(progress, 0, maxVal, 0, barLength);

  for(int i = 0; i < barLength; i++) {
    if(i < filledBars) {
      lcd.write(byte(4)); // Star for filled
    } else {
      lcd.print("-");
    }
  }
}

// Loading Animation Function
void showLoadingAnimation() {
  lcd.clear();
  lcd.setCursor(2, 0);
  lcd.print("Loading Garden");

  for(int i = 0; i <= 10; i++) {
    lcd.setCursor(3, 1);
    drawProgressBar(3, 1, i, 10);
    delay(200);
  }
}



void showMainScreen() {
  // Beautiful Main Screen for Smart Farm/Garden System
  // Top Row: Title and Mode with Agricultural Theme (update only when needed)
  if(forceDisplayUpdate) {
    lcd.setCursor(0, 0);
    lcd.write(byte(0)); // Plant symbol
    lcd.print(" Smart Farm ");
    if(manualMode) {
      lcd.print("M");
    } else {
      lcd.print("A");
    }
  }

  // Show status indicators with beautiful agricultural symbols (update for blinking)
  if(noWaterAlarm1 || noWaterAlarm2) {
    lcd.setCursor(14, 0);
    if(blinkState) {
      lcd.write(byte(3)); // Alert symbol blinking
    } else {
      lcd.print(" "); // Clear when not blinking
    }
    lcd.setCursor(15, 0);
    lcd.print(" "); // Clear position 15
  } else if(irrigationActive1 || irrigationActive2) {
    lcd.setCursor(14, 0);
    lcd.write(byte(1)); // Water drop symbol
    lcd.setCursor(15, 0);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star
    } else {
      lcd.print(" ");
    }
  } else if(smartScheduleEnabled && !isIrrigationTimeAllowed) {
    // Show schedule restriction indicator
    lcd.setCursor(14, 0);
    if(blinkState) {
      // Show season indicator
      if(currentSeason == 0) lcd.write(byte(7)); // Winter (empty box as snowflake)
      else if(currentSeason == 1) lcd.write(byte(5)); // Spring (leaf)
      else if(currentSeason == 2) lcd.write(byte(6)); // Summer (sun)
      else lcd.write(byte(0)); // Autumn (plant)
    } else {
      lcd.print(" ");
    }
    lcd.setCursor(15, 0);
    lcd.write(byte(3)); // Alert for time restriction
  } else {
    lcd.setCursor(14, 0);
    lcd.write(byte(6)); // Sun symbol (all good)
    lcd.setCursor(15, 0);
    if(smartScheduleEnabled) {
      // Show season indicator when smart schedule is active
      if(currentSeason == 0) lcd.write(byte(7)); // Winter (empty box as snowflake)
      else if(currentSeason == 1) lcd.write(byte(5)); // Spring (leaf)
      else if(currentSeason == 2) lcd.write(byte(6)); // Summer (sun)
      else lcd.write(byte(0)); // Autumn (plant)
    } else {
      lcd.write(byte(2)); // Heart symbol
    }
  }

  // Bottom Row: Zone Information (alternates every 2 seconds) - Smart Update
  static bool lastZoneDisplay = false;
  bool currentZoneDisplay = (millis() / 2000) % 2 == 0;

  // Only clear and redraw bottom row when zone display changes or forced update
  if(currentZoneDisplay != lastZoneDisplay || forceDisplayUpdate) {
    lcd.setCursor(0, 1);
    lcd.print("                "); // Clear entire bottom row
    lcd.setCursor(0, 1);
    lastZoneDisplay = currentZoneDisplay;
  }

  // Update zone display
  if(currentZoneDisplay) {
    // Display Zone 1 with Z prefix
    if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
      lcd.setCursor(0, 1);
      lcd.print("Z1:");
    }

    if(!zoneEnabled1) {
      if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
        lcd.setCursor(3, 1);
        lcd.print("  DISABLED!  ");
      }
    } else {
      // Update blinking water drop
      lcd.setCursor(3, 1);
      if(irrigationActive1 && blinkState) {
        lcd.write(byte(1)); // Water drop blinking
      } else {
        lcd.print(" ");
      }

      // Update moisture data (only when forced or zone changed)
      if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
        int moisture1Percent = map(1023 - moistureZone1, 0, 1023, 0, 100);
        lcd.setCursor(4, 1);
        if(moisture1Percent < 10) lcd.print(" ");
        lcd.print(moisture1Percent);
        lcd.print("% ");
        drawMoistureBar(9, 1, moisture1Percent);
      }
    }
  } else {
    // Display Zone 2 with Z prefix
    if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
      lcd.setCursor(0, 1);
      lcd.print("Z2:");
    }

    if(!zoneEnabled2) {
      if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
        lcd.setCursor(3, 1);
        lcd.print("  DISABLED!  ");
      }
    } else {
      // Update blinking water drop
      lcd.setCursor(3, 1);
      if(irrigationActive2 && blinkState) {
        lcd.write(byte(1)); // Water drop blinking
      } else {
        lcd.print(" ");
      }

      // Update moisture data (only when forced or zone changed)
      if(forceDisplayUpdate || currentZoneDisplay != lastZoneDisplay) {
        int moisture2Percent = map(1023 - moistureZone2, 0, 1023, 0, 100);
        lcd.setCursor(4, 1);
        if(moisture2Percent < 10) lcd.print(" ");
        lcd.print(moisture2Percent);
        lcd.print("% ");
        drawMoistureBar(9, 1, moisture2Percent);
      }
    }
  }
}





void showStatusScreen() {
  // Beautiful System Status Screen for Farm/Garden - Smart Update

  // Alternate between different status displays every 3 seconds
  static bool statusDisplay = false;
  bool currentStatusDisplay = (millis() / 3000) % 2 == 0;

  // Only clear and redraw when status display changes or forced update
  if(currentStatusDisplay != statusDisplay || forceDisplayUpdate) {
    lcd.setCursor(0, 0);
    lcd.print("                "); // Clear entire top row
    lcd.setCursor(0, 1);
    lcd.print("                "); // Clear entire bottom row
    statusDisplay = currentStatusDisplay;
  }

  if(currentStatusDisplay) {
    // Display 1: Mode and Time
    if(forceDisplayUpdate || currentStatusDisplay != statusDisplay) {
      lcd.setCursor(0, 0);
      lcd.write(byte(6)); // Sun symbol
      lcd.print(" Mode: ");
      if(manualMode) {
        lcd.print("MANUAL");
      } else {
        lcd.print("AUTO ");
      }
      lcd.setCursor(15, 0);
      lcd.write(byte(2)); // Heart symbol

      // Show current time if RTC is available
      lcd.setCursor(0, 1);
      lcd.write(byte(4)); // Star symbol
      lcd.print(" ");
      lcd.print(getCurrentTimeString());
      lcd.print(" ");
      lcd.print(getCurrentDateString());
      lcd.print(" ");
      lcd.write(byte(4)); // Star symbol
    }
  } else {
    // Display 2: System Status and Schedule
    // System Status with Agricultural Theme - Update for blinking
    lcd.setCursor(0, 1);
    if(irrigationActive1 || irrigationActive2) {
      if(blinkState) {
        lcd.write(byte(1)); // Water drop blinking
        lcd.print(" IRRIGATING ");
      } else {
        lcd.write(byte(4)); // Star
        lcd.print(" IRRIGATING ");
      }
    } else if(noWaterAlarm1 || noWaterAlarm2) {
      if(blinkState) {
        lcd.write(byte(3)); // Alert symbol blinking
        lcd.print(" CHECK WATER");
      } else {
        lcd.print("  CHECK WATER");
      }
    } else if(smartScheduleEnabled && !isIrrigationTimeAllowed) {
      if(forceDisplayUpdate || currentStatusDisplay != statusDisplay) {
        lcd.write(byte(3)); // Alert symbol
        lcd.print(" TIME RESTRICT");
      }
    } else {
      if(forceDisplayUpdate || currentStatusDisplay != statusDisplay) {
        lcd.write(byte(0)); // Plant symbol
        lcd.print(" FARM READY ");
      }
    }

    // Top row: Smart Schedule Status
    if(forceDisplayUpdate || currentStatusDisplay != statusDisplay) {
      lcd.setCursor(0, 0);
      if(smartScheduleEnabled) {
        lcd.write(byte(4)); // Star symbol
        lcd.print(" Schedule: ON ");
        // Show season indicator
        if(currentSeason == 0) lcd.write(byte(7)); // Winter
        else if(currentSeason == 1) lcd.write(byte(5)); // Spring
        else if(currentSeason == 2) lcd.write(byte(6)); // Summer
        else lcd.write(byte(0)); // Autumn
      } else {
        lcd.write(byte(6)); // Sun symbol
        lcd.print(" Schedule: OFF");
      }
    }
  }

  // Beautiful status indicator - Always update for animation
  lcd.setCursor(15, 1);
  if(blinkState) {
    lcd.write(byte(4)); // Sparkling star
  } else {
    lcd.write(byte(5)); // Leaf
  }
}







// Beautiful Settings Screen Function
void showSettingsScreen() {
  int currentZone = displayMode - 1; // displayMode 2-5 = zones 1-4
  int currentThreshold;

  switch(currentZone) {
    case 1: currentThreshold = moistureThresholdPercentZone1; break;
    case 2: currentThreshold = moistureThresholdPercentZone2; break;
    case 3: currentThreshold = moistureThresholdPercentZone3; break;
    case 4: currentThreshold = moistureThresholdPercentZone4; break;
    default: currentThreshold = 30; break;
  }

  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" SETUP Z");
  lcd.print(currentZone);
  lcd.print(" ");
  if(currentZone == 1) {
    lcd.write(byte(5)); // Leaf for Zone 1
  } else if(currentZone == 2) {
    lcd.write(byte(7)); // Flower for Zone 2
  } else if(currentZone == 3) {
    lcd.write(byte(0)); // Plant for Zone 3
  } else {
    lcd.write(byte(6)); // Sun for Zone 4
  }
  lcd.write(byte(4)); // Star symbol

  lcd.setCursor(0, 1);

  // Display current percentage with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Threshold: ---%");
  } else {
    lcd.print("Threshold: ");
    if(currentThreshold < 10) {
      lcd.print(" ");
    }
    if(currentThreshold < 100) {
      lcd.print(" ");
    }
    lcd.print(currentThreshold);
    lcd.print("%");

    // Visual moisture level indicator (10-100% range)
    if(currentThreshold >= 80) {
      lcd.write(byte(6)); // Sun (very high)
    } else if(currentThreshold >= 60) {
      lcd.write(byte(0)); // Plant (high)
    } else if(currentThreshold >= 40) {
      lcd.write(byte(5)); // Leaf (medium)
    } else {
      lcd.write(byte(3)); // Alert (low)
    }
  }

  // Show editing indicator with beautiful agricultural symbol
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// Beautiful Zone 1 Control Screen Function
void showZone1ControlScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" ZONE 1 CTRL ");
  lcd.write(byte(5)); // Leaf symbol for Zone 1

  lcd.setCursor(0, 1);

  // Display Zone 1 status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Z1: -------- ");
  } else {
    lcd.print("Z1: ");
    if(zoneEnabled1) {
      lcd.write(byte(6)); // Sun (ACTIVE)
      lcd.print(" ACTIVE   ");
      if(irrigationActive1) {
        lcd.write(byte(1)); // Water drop
      } else {
        lcd.write(byte(2)); // Heart
      }
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print(" DISABLED ");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// Beautiful Zone 2 Control Screen Function
void showZone2ControlScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" ZONE 2 CTRL ");
  lcd.write(byte(7)); // Empty box symbol for Zone 2

  lcd.setCursor(0, 1);

  // Display Zone 2 status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Z2: -------- ");
  } else {
    lcd.print("Z2: ");
    if(zoneEnabled2) {
      lcd.write(byte(6)); // Sun (ACTIVE)
      lcd.print(" ACTIVE   ");
      if(irrigationActive2) {
        lcd.write(byte(1)); // Water drop
      } else {
        lcd.write(byte(2)); // Heart
      }
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print(" DISABLED ");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// ===============================
// EEPROM Settings Management
// ===============================

// Save all settings to EEPROM with visual feedback
void saveSettingsToEEPROM() {
  // Write magic number to verify valid data
  EEPROM.write(EEPROM_ADDR_MAGIC, EEPROM_MAGIC_NUMBER & 0xFF);
  EEPROM.write(EEPROM_ADDR_MAGIC + 1, (EEPROM_MAGIC_NUMBER >> 8) & 0xFF);

  // Save moisture thresholds
  EEPROM.write(EEPROM_ADDR_ZONE1_THRESHOLD, moistureThresholdPercentZone1);
  EEPROM.write(EEPROM_ADDR_ZONE2_THRESHOLD, moistureThresholdPercentZone2);
  EEPROM.write(EEPROM_ADDR_ZONE3_THRESHOLD, moistureThresholdPercentZone3);
  EEPROM.write(EEPROM_ADDR_ZONE4_THRESHOLD, moistureThresholdPercentZone4);

  // Save zone enabled status
  EEPROM.write(EEPROM_ADDR_ZONE1_ENABLED, zoneEnabled1 ? 1 : 0);
  EEPROM.write(EEPROM_ADDR_ZONE2_ENABLED, zoneEnabled2 ? 1 : 0);
  EEPROM.write(EEPROM_ADDR_ZONE3_ENABLED, zoneEnabled3 ? 1 : 0);
  EEPROM.write(EEPROM_ADDR_ZONE4_ENABLED, zoneEnabled4 ? 1 : 0);

  // Save buzzer enabled status
  EEPROM.write(EEPROM_ADDR_BUZZER_ENABLED, buzzerEnabled ? 1 : 0);

  // Save smart schedule enabled status
  EEPROM.write(EEPROM_ADDR_SMART_SCHEDULE, smartScheduleEnabled ? 1 : 0);

  // Brief visual and audio feedback for successful save
  playBuzzerPattern(7); // Success sound
  lcd.setCursor(14, 0);
  lcd.write(byte(4)); // Star to indicate save
  delay(200);
  lcd.setCursor(14, 0);
  lcd.print(" "); // Clear indicator
}

// Load all settings from EEPROM
void loadSettingsFromEEPROM() {
  // Read magic number to verify valid data
  int magicLow = EEPROM.read(EEPROM_ADDR_MAGIC);
  int magicHigh = EEPROM.read(EEPROM_ADDR_MAGIC + 1);
  unsigned int storedMagic = magicLow | (magicHigh << 8);

  if(storedMagic == EEPROM_MAGIC_NUMBER) {
    // Valid data found, load settings
    moistureThresholdPercentZone1 = EEPROM.read(EEPROM_ADDR_ZONE1_THRESHOLD);
    moistureThresholdPercentZone2 = EEPROM.read(EEPROM_ADDR_ZONE2_THRESHOLD);
    moistureThresholdPercentZone3 = EEPROM.read(EEPROM_ADDR_ZONE3_THRESHOLD);
    moistureThresholdPercentZone4 = EEPROM.read(EEPROM_ADDR_ZONE4_THRESHOLD);
    zoneEnabled1 = EEPROM.read(EEPROM_ADDR_ZONE1_ENABLED) == 1;
    zoneEnabled2 = EEPROM.read(EEPROM_ADDR_ZONE2_ENABLED) == 1;
    zoneEnabled3 = EEPROM.read(EEPROM_ADDR_ZONE3_ENABLED) == 1;
    zoneEnabled4 = EEPROM.read(EEPROM_ADDR_ZONE4_ENABLED) == 1;
    buzzerEnabled = EEPROM.read(EEPROM_ADDR_BUZZER_ENABLED) == 1;
    smartScheduleEnabled = EEPROM.read(EEPROM_ADDR_SMART_SCHEDULE) == 1;

    // Validate loaded values and correct if necessary
    if(moistureThresholdPercentZone1 < 10 || moistureThresholdPercentZone1 > 100) {
      moistureThresholdPercentZone1 = 30; // Default value
    }
    if(moistureThresholdPercentZone2 < 10 || moistureThresholdPercentZone2 > 100) {
      moistureThresholdPercentZone2 = 30; // Default value
    }
    if(moistureThresholdPercentZone3 < 10 || moistureThresholdPercentZone3 > 100) {
      moistureThresholdPercentZone3 = 30; // Default value
    }
    if(moistureThresholdPercentZone4 < 10 || moistureThresholdPercentZone4 > 100) {
      moistureThresholdPercentZone4 = 30; // Default value
    }

    // Display loaded settings briefly with success sound
    playBuzzerPattern(7); // Success sound for loaded settings
    lcd.setCursor(0, 1);
    lcd.print("Z1:");
    lcd.print(moistureThresholdPercentZone1);
    lcd.print("% Z2:");
    lcd.print(moistureThresholdPercentZone2);
    lcd.print("%");
    delay(1500);
  } else {
    // No valid data found, use defaults and save them
    lcd.setCursor(0, 1);
    lcd.print("First Time Setup");
    delay(1000);
    resetSettingsToDefault();
    saveSettingsToEEPROM();
  }
}

// Reset all settings to default values
// Note: To manually reset settings, you can call this function or
// clear EEPROM by writing different magic number
void resetSettingsToDefault() {
  moistureThresholdPercentZone1 = 30;  // Default 30%
  moistureThresholdPercentZone2 = 30;  // Default 30%
  zoneEnabled1 = true;                 // Zone 1 enabled by default
  zoneEnabled2 = true;                 // Zone 2 enabled by default
  buzzerEnabled = true;                // Buzzer enabled by default
  smartScheduleEnabled = false;        // Smart schedule disabled by default
}

// Beautiful Zone 3 Control Screen Function
void showZone3ControlScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" ZONE 3 CTRL ");
  lcd.write(byte(0)); // Plant symbol for Zone 3

  lcd.setCursor(0, 1);

  // Display Zone 3 status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Z3: -------- ");
  } else {
    lcd.print("Z3: ");
    if(zoneEnabled3) {
      lcd.write(byte(6)); // Sun (ACTIVE)
      lcd.print(" ACTIVE   ");
      if(irrigationActive3) {
        lcd.write(byte(1)); // Water drop
      } else {
        lcd.write(byte(2)); // Heart
      }
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print(" DISABLED ");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// Beautiful Zone 4 Control Screen Function
void showZone4ControlScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" ZONE 4 CTRL ");
  lcd.write(byte(6)); // Sun symbol for Zone 4

  lcd.setCursor(0, 1);

  // Display Zone 4 status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Z4: -------- ");
  } else {
    lcd.print("Z4: ");
    if(zoneEnabled4) {
      lcd.write(byte(6)); // Sun (ACTIVE)
      lcd.print(" ACTIVE   ");
      if(irrigationActive4) {
        lcd.write(byte(1)); // Water drop
      } else {
        lcd.write(byte(2)); // Heart
      }
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print(" DISABLED ");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// Beautiful Buzzer Settings Screen Function
void showBuzzerSettingsScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" BUZZER SETUP ");
  lcd.write(byte(4)); // Star symbol

  lcd.setCursor(0, 1);

  // Display buzzer status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Sound: -------- ");
  } else {
    lcd.print("Sound: ");
    if(buzzerEnabled) {
      lcd.write(byte(6)); // Sun (ENABLED)
      lcd.print(" ENABLED  ");
      lcd.write(byte(1)); // Water drop
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print(" DISABLED ");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

// ===============================
// BUZZER SYSTEM FUNCTIONS
// ===============================

// Play a simple tone on the passive buzzer
void playTone(int frequency, int duration) {
  if(!buzzerEnabled) return; // Skip if buzzer is disabled

  if(frequency > 0) {
    // Generate PWM signal for passive buzzer
    tone(BUZZER_PIN, frequency, duration);
  } else {
    noTone(BUZZER_PIN);
  }
}

// Start a passive buzzer pattern
void playBuzzerPattern(int pattern) {
  if(!buzzerEnabled) return; // Skip if buzzer is disabled

  buzzerPattern = pattern;
  buzzerPatternStep = 0;
  buzzerPatternStartTime = millis();
}

// Update passive buzzer patterns (called in main loop)
void updateBuzzer() {
  if(!buzzerEnabled || buzzerPattern == 0) return; // Skip if buzzer disabled or no pattern

  unsigned long currentTime = millis();
  unsigned long elapsed = currentTime - buzzerPatternStartTime;

  switch(buzzerPattern) {
    case 1: // Water alarm pattern - PROBLEM DETECTED (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(2000, 400); // VERY HIGH problem alert beep - MAXIMUM VOLUME
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 450) {
        playTone(1000, 350); // LOUD low problem alert beep
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 400) {
        playTone(2000, 400); // VERY HIGH problem alert beep again - MAXIMUM VOLUME
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 450) {
        playTone(1500, 500); // LOUD final warning beep - EXTENDED
        buzzerPatternStep = 4;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 4 && elapsed >= 550) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 2: // Irrigation start pattern - LOUD ascending tones (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(800, 300); // LOUD low tone - INCREASED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 350) {
        playTone(1200, 300); // LOUD medium tone - INCREASED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 350) {
        playTone(1600, 400); // VERY LOUD high tone - MAXIMUM VOLUME
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 450) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 3: // System ready pattern - LOUD cheerful melody (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1046, 250); // HIGH C note - DOUBLED FREQUENCY & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 300) {
        playTone(1318, 250); // HIGH E note - DOUBLED FREQUENCY & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 300) {
        playTone(1568, 400); // HIGH G note - DOUBLED FREQUENCY & MUCH LONGER
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 450) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 4: // Button press pattern - LOUD short click (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1800, 150); // VERY LOUD quick beep - MUCH HIGHER & LONGER
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 180) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 5: // Settings change pattern - LOUD double beep (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1700, 200); // LOUD first beep - MUCH HIGHER & LONGER
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 250) {
        playTone(1700, 200); // LOUD second beep - MUCH HIGHER & LONGER
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 250) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 6: // Welcome melody - LOUD beautiful garden theme (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1046, 350); // HIGH C note - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 400) {
        playTone(1318, 350); // HIGH E note - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 400) {
        playTone(1568, 350); // HIGH G note - DOUBLED & EXTENDED
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 400) {
        playTone(2094, 500); // VERY HIGH C note - DOUBLED & MUCH LONGER
        buzzerPatternStep = 4;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 4 && elapsed >= 550) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 7: // Success sound - LOUD cheerful completion (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1568, 250); // HIGH G note - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 300) {
        playTone(2094, 250); // VERY HIGH C note - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 300) {
        playTone(2637, 400); // EXTREMELY HIGH E note - DOUBLED & MUCH LONGER
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 450) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 8: // Zone activation sound - LOUD positive feedback (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1318, 200); // HIGH E note - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 250) {
        playTone(1568, 200); // HIGH G note - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 250) {
        playTone(2094, 300); // VERY HIGH C note - DOUBLED & MUCH LONGER
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 350) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;

    case 9: // Happy irrigation sound - LOUD playful water theme (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1046, 180); // HIGH C note - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 220) {
        playTone(1318, 180); // HIGH E note - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 220) {
        playTone(1568, 180); // HIGH G note - DOUBLED & EXTENDED
        buzzerPatternStep = 3;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 3 && elapsed >= 220) {
        playTone(1318, 180); // HIGH E note - DOUBLED & EXTENDED
        buzzerPatternStep = 4;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 4 && elapsed >= 220) {
        playTone(1046, 300); // HIGH C note - DOUBLED & MUCH LONGER
        buzzerPatternStep = 5;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 5 && elapsed >= 350) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern
      }
      break;



    case 11: // Professional system startup - LOUD elegant and sophisticated (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1600, 300); // LOUD professional tone - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 350) {
        playTone(2000, 200); // LOUD confirmation tone - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 250) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern - LOUD and professional
      }
      break;

    case 12: // Professional team signature - LOUD and refined (MAXIMUM VOLUME)
      if(buzzerPatternStep == 0 && elapsed >= 0) {
        playTone(1800, 250); // LOUD professional acknowledgment tone - DOUBLED & EXTENDED
        buzzerPatternStep = 1;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 1 && elapsed >= 300) {
        playTone(2200, 180); // LOUD confirmation - DOUBLED & EXTENDED
        buzzerPatternStep = 2;
        buzzerPatternStartTime = currentTime;
      } else if(buzzerPatternStep == 2 && elapsed >= 230) {
        noTone(BUZZER_PIN);
        buzzerPattern = 0; // End pattern - LOUD and professional
      }
      break;

    default:
      buzzerPattern = 0; // Invalid pattern, stop
      noTone(BUZZER_PIN);
      break;
  }
}

// Individual sound functions for easy calling
void playWaterAlarmSound() {
  playBuzzerPattern(1);
}

void playIrrigationStartSound() {
  playBuzzerPattern(2);
}

void playSystemReadySound() {
  playBuzzerPattern(3);
}

void playButtonPressSound() {
  playBuzzerPattern(4);
}

void playSettingsChangeSound() {
  playBuzzerPattern(5);
}

// New enhanced audio functions for better user experience
void playWelcomeMelody() {
  playBuzzerPattern(6);
}

void playSuccessSound() {
  playBuzzerPattern(7);
}

void playZoneActivationSound() {
  playBuzzerPattern(8);
}

void playHappyIrrigationSound() {
  playBuzzerPattern(9);
}

// New critical and intro audio functions


void playIntroSound() {
  playBuzzerPattern(11);
}

void playTeamSound() {
  playBuzzerPattern(12);
}

// ===============================
// SMART SEASONAL SCHEDULE SYSTEM
// ===============================

// Initialize RTC Module
void initializeRTC() {
  if (!rtc.begin()) {
    // RTC not found - show error message
    lcd.setCursor(0, 0);
    lcd.print("RTC Module Error");
    lcd.setCursor(0, 1);
    lcd.print("Check Connection");
    delay(3000);
    return;
  }

  // Check if RTC lost power and set time if needed
  if (rtc.lostPower()) {
    lcd.setCursor(0, 0);
    lcd.print("RTC Lost Power");
    lcd.setCursor(0, 1);
    lcd.print("Time Reset Needed");
    delay(2000);

    // Set RTC to compile time (for initial setup)
    rtc.adjust(DateTime(F(__DATE__), F(__TIME__)));
  }

  // Display RTC initialization success
  lcd.setCursor(0, 0);
  lcd.print("Smart Schedule");
  lcd.setCursor(0, 1);
  lcd.print("RTC Ready!");
  delay(1500);
}

// Update current time from RTC
void updateCurrentTime() {
  static unsigned long lastTimeUpdate = 0;
  unsigned long currentMillis = millis();

  // Update time every 30 seconds to reduce I2C traffic
  if (currentMillis - lastTimeUpdate >= 30000) {
    currentTime = rtc.now();
    currentSeason = getCurrentSeason(currentTime.month());
    isIrrigationTimeAllowed = isIrrigationAllowed();
    lastTimeUpdate = currentMillis;
  }
}

// Get current season based on month
int getCurrentSeason(int month) {
  if (month == 12 || month == 1 || month == 2) {
    return 0; // Winter ❄️
  } else if (month >= 3 && month <= 5) {
    return 1; // Spring 🌱
  } else if (month >= 6 && month <= 8) {
    return 2; // Summer ☀️
  } else {
    return 3; // Autumn 🍂
  }
}

// Check if irrigation is allowed based on current time and season
bool isIrrigationAllowed() {
  if (!smartScheduleEnabled) {
    return true; // Always allow if smart schedule is disabled
  }

  int hour = currentTime.hour();
  int season = getCurrentSeason(currentTime.month());

  switch (season) {
    case 0: // Winter ❄️ (Dec-Feb): 09:00-12:00
      return (hour >= 9 && hour < 12);

    case 1: // Spring 🌱 (Mar-May): 06:00-10:00
      return (hour >= 6 && hour < 10);

    case 2: // Summer ☀️ (Jun-Aug): 05:00-08:00 & 18:00-20:00
      return (hour >= 5 && hour < 8) || (hour >= 18 && hour < 20);

    case 3: // Autumn 🍂 (Sep-Nov): 07:00-10:00
      return (hour >= 7 && hour < 10);

    default:
      return true;
  }
}

// Get season name in Arabic
String getSeasonName(int season) {
  switch (season) {
    case 0: return "Winter";
    case 1: return "Spring";
    case 2: return "Summer";
    case 3: return "Autumn";
    default: return "Unknown";
  }
}

// Get season icon
String getSeasonIcon(int season) {
  switch (season) {
    case 0: return "❄️"; // Winter
    case 1: return "🌱"; // Spring
    case 2: return "☀️"; // Summer
    case 3: return "🍂"; // Autumn
    default: return "?";
  }
}

// Get current time as string (HH:MM)
String getCurrentTimeString() {
  String timeStr = "";
  if (currentTime.hour() < 10) timeStr += "0";
  timeStr += String(currentTime.hour());
  timeStr += ":";
  if (currentTime.minute() < 10) timeStr += "0";
  timeStr += String(currentTime.minute());
  return timeStr;
}

// Get current date as string (DD/MM)
String getCurrentDateString() {
  String dateStr = "";
  if (currentTime.day() < 10) dateStr += "0";
  dateStr += String(currentTime.day());
  dateStr += "/";
  if (currentTime.month() < 10) dateStr += "0";
  dateStr += String(currentTime.month());
  return dateStr;
}

// Beautiful Smart Schedule Settings Screen Function
void showSmartScheduleScreen() {
  lcd.setCursor(0, 0);
  lcd.write(byte(4)); // Star symbol
  lcd.print(" SMART SCHEDULE");
  lcd.write(byte(4)); // Star symbol

  lcd.setCursor(0, 1);

  // Display smart schedule status with beautiful blinking effect during editing
  if(button2LongPressDetected && blinkState) {
    lcd.print("Schedule: ------");
  } else {
    lcd.print("Schedule: ");
    if(smartScheduleEnabled) {
      lcd.write(byte(6)); // Sun (ENABLED)
      lcd.print("ON ");
      // Show current season icon
      if(currentSeason == 0) lcd.write(byte(7)); // Winter (empty box as snowflake)
      else if(currentSeason == 1) lcd.write(byte(5)); // Spring (leaf)
      else if(currentSeason == 2) lcd.write(byte(6)); // Summer (sun)
      else lcd.write(byte(0)); // Autumn (plant)
    } else {
      lcd.write(byte(3)); // Alert (DISABLED)
      lcd.print("OFF");
    }
  }

  // Show editing indicator with beautiful sparkle
  if(button2LongPressDetected) {
    lcd.setCursor(15, 1);
    if(blinkState) {
      lcd.write(byte(4)); // Sparkling star for editing
    } else {
      lcd.print(" ");
    }
  }
}

